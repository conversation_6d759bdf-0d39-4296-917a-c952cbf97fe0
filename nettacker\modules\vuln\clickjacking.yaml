info:
  name: clickjacking_vuln
  author: OWASP Nettacker Team
  severity: 5
  description: "Clickjacking, also known as a \"UI redress attack\", is when an attacker uses multiple transparent or opaque layers to trick a user into clicking on a button"
  reference: "https://cheatsheetseries.owasp.org/cheatsheets/Clickjacking_Defense_Cheat_Sheet.html"
  profiles:
    - vuln
    - vulnerability
    - http
    - medium_severity

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: and
          conditions:
            headers:
              x-frame-options:
                regex: ^((?!SAMEORIGIN|DENY).)+$
                reverse: false
              Content-Security-Policy:
                regex: ^((?!frame-ancestors|frame-src).)+$
                reverse: false
            content:
              regex: ^((?!http-equiv=.*Content-Security-Policy.*content=.*(DENY|SAMEORIGIN)).)+$
              reverse: false
