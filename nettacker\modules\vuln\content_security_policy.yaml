info:
  name: content_security_policy_vuln
  author: OWASP Nettacker Team
  severity: 3
  description: "Content-Security-Policy is the name of a HTTP response header that modern browsers use to enhance the security of the document (or web page). The Content-Security-Policy header allows you to restrict how resources such as JavaScript, CSS, or pretty much anything that the browser loads."
  reference: "https://cheatsheetseries.owasp.org/cheatsheets/Content_Security_Policy_Cheat_Sheet.html"
  profiles:
    - vuln
    - vulnerability
    - http
    - low_severity
    - csp

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: or
          conditions:
            headers:
              Content-Security-Policy:
                regex: ^.{{0,0}}$|unsafe\-\S+
                reverse: false
            content:
              regex: ^((?!Content-Security-Policy unsafe-\S+).)+$
              reverse: false
