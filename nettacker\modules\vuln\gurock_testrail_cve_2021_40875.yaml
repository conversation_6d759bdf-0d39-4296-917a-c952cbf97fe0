info:
  name: gurock_testrail_cve_2021_40875_vuln
  author: OWASP Nettacker Team
  severity: 5
  description: Improper Access Control in Gurock TestRail versions < 7.2.0.3014 resulted in sensitive information exposure. A threat actor can access the /files.md5 file on the client side of a Gurock TestRail application, disclosing a full list of application files and the corresponding file paths. The corresponding file paths can be tested, and in some cases, result in the disclosure of hardcoded credentials, API keys, or other sensitive data.
  reference: 
    - https://www.gurock.com/testrail/tour/enterprise-edition
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-40875
  profiles:
    - vuln
    - vulnerability
    - http
    - medium_severity
    - cve2021
    - cve
    - gurock
    - gurock_testrail

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/{{paths}}"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              paths:
                - files.md5
                - testrail/files.md5
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "200"
              reverse: false
            content:
              regex: app/arguments/admin # could use this but not necessary ^\C{{0,1000}}app/arguments/admin\C{{0,1000}}$
              reverse: false
