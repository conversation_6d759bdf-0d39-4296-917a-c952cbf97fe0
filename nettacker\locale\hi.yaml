---
scan_started: <PERSON><PERSON><PERSON> इंजन शुरू हुआ ...
help_menu: Nettacker सहायता मेनू दिखाएं
license: कृपया लाइसेंस और समझौते पढ़ें https://github.com/OWASP/Nettacker
engine: इंजन
engine_input: इंजन इनपुट विकल्प
select_language: किसी एक भाषा का चयन करें {0}
range: सीमा में सभी आईपी का स्कैन करें
subdomains: सबडोमेन ढूंढें और स्कैन करें
thread_number_connections: एक मेजबान के कनेक्शन के लिए थ्रेड की संख्या
save_logs: फ़ाइल में सभी लॉग सहेजें (results.txt, results.html, results.json)
target: लक्ष्य
target_input: लक्ष्य इनपुट विकल्प
target_list: लक्ष्य(ओं) की सूची, "," से अलग
read_target: फ़ाइल से लक्ष्य(ओं) की सूची पढ़ें
scan_method_options: स्कैन मॉड्यूल विकल्प
choose_scan_method: स्कैन मॉड्यूल का चयन करें {0}, पूरी सूची देखने के लिए '--show-all-modules'
exclude_scan_method: "{0} बाहर करने के लिए स्कैन मॉड्यूल का चयन करें"
username_list: उपयोगकर्ता नाम (ओं) सूची, "," से अलग
username_from_file: फ़ाइल से उपयोगकर्ता नाम (ओं) पढ़ें
password_separator: पासवर्ड (ओं) सूची, "," से अलग
read_passwords: फ़ाइल से पासवर्ड पढ़ें
port_separator: पोर्ट (ओं) सूची, "," से अलग
time_to_sleep: प्रत्येक अनुरोध के बीच रुकने के लिए समय
error_target: लक्ष्य निर्दिष्ट नहीं कर सकते
error_target_file:
  "फ़ाइल को खोलने में असमर्थ, लक्ष्य (लक्ष्य) निर्दिष्ट नहीं कर सकते:
  {0}"
scan_module_not_found: यह स्कैन मॉड्यूल [{0}] नहीं मिला!
error_username: "फ़ाइल खोलने में असमर्थ, उपयोगकर्ता नाम निर्दिष्ट नहीं कर सकते: {0}"
file_write_error: फ़ाइल "{0}" लिखने योग्य नहीं है!
scan_method_select: कृपया अपनी स्कैन मॉड्यूल चुनें!
done: आरंभ।
error_platform: दुर्भाग्यवश सॉफ़्टवेयर का यह संस्करण बस लिनक्स / डार्विन / विंडोज़ पर चलाया जा सकता है।
verbose_mode: वर्बोज़ मोड स्तर (0-5) (डिफ़ॉल्ट 0)
software_version: सॉफ्टवेयर संस्करण
outgoing_proxy:
  "आउटगोइंग कनेक्शन प्रॉक्सी (सॉक्स)। उदाहरण सॉक्स 5: 127.0.0.1:9050,
  सॉक्स : //127.0.0.1:9050 सॉक्स 5://127.0.0.1:9050 या सॉक्स 4: सॉक्स 4://127.0.0.1:9050,
  प्रमाणीकरण: सॉक्स://उपयोगकर्ता नाम: पासवर्ड@127.0.0.1, सॉक्स 4://उपयोगकर्ता नाम:पासवर्ड@127.0.0.1,
  सॉक्स 5://उपयोगकर्ता नाम:पासवर्ड@127.0.0.1"
connection_retries: कनेक्शन टाइमआउट (डिफ़ॉल्ट 3) पर पुनः प्रयास करता है।
current_version: आप OWASP Nettacker संस्करण {0} {1} {2} {6} कोड नाम {3} {4} {5} चला रहे हैं
available_graph: "सभी गतिविधियों और जानकारी का एक ग्राफ बनाएं, आपको HTML आउटपुट का उपयोग करना होगा। उपलब्ध ग्राफ: {0}"
graph_output: ग्राफ़ सुविधा का उपयोग करने के लिए आपका आउटपुट फ़ाइल नाम ".html" या ".htm" के साथ समाप्त होना चाहिए।
build_graph: ग्राफ निर्माण ...
finish_build_graph: निर्माण ग्राफ खत्म हुआ।
pentest_graphs: प्रवेश परीक्षण ग्राफ
graph_message:
  यह ग्राफ OWASP Nettacker द्वारा बनाया गया है। ग्राफ़ में सभी मॉड्यूल गतिविधियां,
  नेटवर्क मानचित्र और संवेदनशील जानकारी शामिल है,
  अगर यह विश्वसनीय नहीं है तो कृपया इस फ़ाइल को किसी के साथ साझा न करें।
nettacker_report: OWASP Nettacker रिपोर्ट
nettacker_version_details: "सॉफ्टवेयर विवरण: OWASP Nettacker संस्करण {0} [{1}] {2} में"
loaded_modules: "{0} मॉड्यूल लोड हुए..."
graph_module_404: "यह ग्राफ मॉड्यूल नहीं मिला: {0}"
graph_module_unavailable: यह ग्राफ मॉड्यूल "{0}" उपलब्ध नहीं है
ping_before_scan: मेजबान स्कैन करने से पहले पिंग
select_profile: प्रोफ़ाइल का चयन करें {0}
profile_404: प्रोफाइल "{0}" नहीं मिला!
Method: तरीका
API: API
API_options: API विकल्प
start_api_server: API सेवा शुरू करें
API_host: API होस्ट पता
API_port: API पोर्ट नंबर
API_debug: API डीबग मोड
API_access_key: API एक्सेस कुंजी
define_white_list: "श्वेत सूची होस्ट को परिभाषित करें, ',' से अलग, (उदाहरण: 127.0.0.1, 1 9 *********/24, ********-**********)"
API_access_log_file: API एक्सेस लॉग फ़ाइल नाम
API_key: "* API का उपयोग किया जा सकता है https://nettacker-api.z3r0d4y.com:{0}/ के माध्यम से API कुंजी: {1}"
ports_int: पोर्ट्स को पूर्णांक होने चाहिए! (उदाहरण के लिए 80 || 80,1080 || 80,1080-1300,9000,12000-15000)
API_invalid: अवैध API कुंजी
API_cert: API प्रमाणपत्र
API_cert_key: API प्रमाणपत्र कुंजी
unauthorized_IP: आपका आईपी अधिकृत नहीं है
not_found: नहीं मिला!
browser_session_valid: आपका ब्राउज़र सत्र मान्य है
browser_session_killed: आपका ब्राउज़र सत्र समाप्त हो गया
updating_database: डेटाबेस अपडेट किया जा रहा है ...
database_connect_fail: डेटाबेस से कनेक्ट नहीं किया जा सका!
inserting_report_db: डेटाबेस में रिपोर्ट डल रही है
removing_logs_db: डीबी से पुराने लॉग को हटाया जा रहा है
invalid_database: कृपया कॉन्फ़िगरेशन फ़ाइल में MySQL या sqlite से चुनें
database_connection_failed: चयनित डाटाबेस से कनेक्शन विफल हुआ
file_saved: रिपोर्ट डेटाबेस और {0} में सहेजी गई
skip_service_discovery: स्कैन से पहले सेवा खोज को छोड़ें और सभी मॉड्यूल को फिर भी स्कैन करने को बदलें
no_live_service_found: कोई भी लाइव सेवा स्कैन करने के लिए नहीं मिली।
icmp_need_root_access: icmp_scan मॉड्यूल या --ping-before-scan का उपयोग करने के लिए आपको स्क्रिप्ट को रूट के रूप में चलाने की आवश्यकता है!
filtered_content: ... [रिपोर्ट में पूर्ण सामग्री देखें]
Invalid_whatcms_api_key: "{0}"
searching_whatcms_database: "whatcms.org पर CMS की खोज ..."
whatcms_monthly_quota_exceeded: आपने अपने मासिक WHTCMS अनुरोध कोटा को पार कर लिया है
finished_module: लक्ष्य {1} की ओर मॉड्यूल {0} पूरा हो गया | मॉड्यूल थ्रेड नंबर {2} से {3} से
modules_extra_args_help: मॉड्यूल को पास करने के लिए अतिरिक्त args जोड़ें (जैसे, --modules-extra-args "x_api_key=123&xyz_passwd=abc")
cannot_run_api_server: आप स्वयं के माध्यम से API सर्वर नहीं चला सकते हैं!
library_not_supported: लाइब्रेरी [{0}] समर्थित नहीं है!
removing_old_db_records: चयनित लक्ष्यों और मॉड्यूल के लिए पुराने डेटाबेस रिकॉर्ड को हटा रहा है।
regrouping_targets: हार्डवेयर संसाधनों के आधार पर लक्ष्यों को फिर से संगठित किया जा रहा है!
finished_parallel_module_scan: प्रक्रिया- {0} | {1} | {2} | मॉड्यूल थ्रेड संख्या {3}, {4} से समाप्त
invalid_json_type_to_db: "डेटाबेस के लिए JSON डेटा का अमान्य प्रकार। डेटाबेस को सब्बिशन छोड़ा जा रहा है। डेटा: {0}"
loading_modules: सभी मॉड्यूल लोड हो रहा है ... यह कुछ समय हो सकता है!
loading_profiles: सभी प्रोफाइल लोड हो रहा है ... यह कुछ समय ले सकता है!
module_profile_full_information: "{0} {1} {2}: {3}"
select_user_agent: "HTTP अनुरोधों के साथ भेजने के लिए एक उपयोगकर्ता एजेंट का चयन करें या अनुरोधों में उपयोगकर्ता-एजेंट को यादृच्छिक करने के लिए \"random_user_agent\" दर्ज करें।"
send_success_event_from_module: "प्रक्रिया- {0} | {1} | {2} | मॉड्यूल-थ्रेड {3} / {4} | अनुरोध-धागा {5} / {6} | {7} | success_condition (s): {8}"
send_unsuccess_event_from_module: "प्रक्रिया- {0} | {1} | {2} | मॉड्यूल-थ्रेड {3} / {4} | अनुरोध-थ्रेड {5} / {6} | सभी शर्तें विफल"
sending_module_request: " प्रक्रिया- {0} | {1} | {2} | मॉड्यूल-थ्रेड {3} / {4} | {6} से अनुरोध {5} भेजी जा रहा है"
verbose_event: प्रत्येक थ्रेड की स्थिति देखने के लिए verbose घटना को सक्षम करें
no_events_for_report: रिपोर्ट बनाने के लिए कोई कार्यक्रम मौजूद नहीं है!इस खंड को छोड़ा जा रहा है।
set_hardware_usage: स्कैनिंग करते समय हार्डवेयर उपयोग सेट करें। (low, normal, high, maximum)
show_all_modules: सभी मॉड्यूल और उनकी जानकारी दिखाएं
show_all_profiles: सभी प्रोफाइल और उनकी जानकारी दिखाएं
single_process_started: प्रक्रिया- {0} |प्रक्रिया शुरू हो हो चुकी है!
start_multi_process: '{1} प्रक्रिया(ओं) में {0} लक्ष्य आयात किए गए'
start_parallel_module_scan: प्रक्रिया- {0} | {1} | {2} |{4} से मॉड्यूल थ्रेड नंबर {3} प्रारंभ किया
thread_number_modules: मेजबान के लिए समानांतर मॉड्यूल स्कैन
wrong_hardware_usage: "आपको हार्डवेयर उपयोग के लिए इनमें से एक प्रोफाइल का चयन करना होगा। (low, normal, high, maximum)"
invalid_scan_id: आपकी स्कैन आईडी मान्य नहीं है
compare_scans: scan_id का उपयोग करके वर्तमान स्कैन की पुराने स्कैन से तुलना करें
compare_report_path_filename: तुलना रिपोर्ट सहेजने का फ़ाइल पथ
no_scan_to_compare: तुलना करने के लिए scan_id नहीं मिला
compare_report_saved: "{0} में तुलना परिणाम सहेजे गए"
build_compare_report: "तुलना रिपोर्ट बनाई जा रही है"
finish_build_report: "तुलना रिपोर्ट तैयार हो गई"