info:
  name: wordpress_version_scan
  author: OWASP Nettacker Team
  severity: 3
  description: Directory, Backup finder
  reference:
  profiles:
    - scan
    - http
    - backup
    - low_severity
    - wp
    - wordpress
payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/wp-content/plugins/{{paths}}/readme.txt"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
              #We are using small txt file. Work need to be done for handling user input based format files
              paths:
                read_from_file: wordlists/wp_plugin_small.txt            
        response:
          condition_type: and
          conditions:
            content:
              regex: Stable\stag:\s(\d+\.\d+\.\d+)
              reverse: false
            status_code:
              regex: "200"
              reverse: false
