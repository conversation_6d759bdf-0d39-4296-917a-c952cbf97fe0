repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-ast
      - id: check-builtin-literals
      - id: check-yaml
      - id: fix-encoding-pragma
        args:
          - --remove
      - id: mixed-line-ending
        args:
          - --fix=lf

  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.13
    hooks:
      - id: ruff
        args:
          - --fix
      - id: ruff-format
