# OWASP Nettacker

###  Table of contents sections are also visible in the sidebar (right).

<img src="https://raw.githubusercontent.com/OWASP/Nettacker/master/nettacker/web/static/img/owasp-nettacker.png" width="200"><img src="https://raw.githubusercontent.com/OWASP/Nettacker/master/nettacker/web/static/img/owasp.png" width="500">


- [Introduction](#introduction)
  * [Links](#links)
- [Installation](Installation.md)
- [Usage](Usage.md)

# Introduction


OWASP Nettacker is open-source software written in Python language using **YAML-type** modules that let you automate penetration testing and Information Gathering. This software aims to have all security tests you can do in a network, such as vulnerability scan and management (with or without CVE), brute force attacks, misconfiguration, and more. The purpose of this project is to speed up internal and external security assessments.

![OWASP Nettacker](https://user-images.githubusercontent.com/7676267/35123376-283d5a3e-fcb7-11e7-9b1c-92b78ed4fecc.gif)

## Links

* OWASP Page: https://www.owasp.org/nettacker
* Wiki: https://github.com/OWASP/Nettacker/wiki
* GitHub: https://github.com/OWASP/Nettacker
* Official Docker Image: https://hub.docker.com/r/owasp/nettacker/
* Slack: #project-nettacker on https://owasp.slack.com  (OWASP Slack inivite at https://owasp.org/slack/invite)

* OpenHub: https://www.openhub.net/p/OWASP-Nettacker
* CI: https://github.com/OWASP/Nettacker/actions
* **Donate**: [https://www.owasp.org/](https://owasp.org/donate/?reponame=www-project-nettacker&title=OWASP+Nettacker)
* Original Creator/Maintainer: https://www.secologist.com/
