info:
  name: pma_scan
  author: OWASP Nettacker Team
  severity: 3
  description: php my admin finder
  reference:
  profiles:
    - scan
    - http
    - backup
    - low_severity

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/{{urls}}"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              urls:
                read_from_file: wordlists/pma_wordlist.txt
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: or
          conditions:
            status_code:
              regex: 200|403|401
              reverse: false
