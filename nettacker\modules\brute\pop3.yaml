info:
  name: pop3_brute
  author: <PERSON><PERSON><PERSON> Bhowmick
  severity: 3
  description: POP3 Bruteforcer
  reference:
  profiles:
    - brute
    - brute_force
    - pop3

payloads:
  - library: pop3
    steps:
      - method: brute_force
        timeout: 3
        host: "{target}"
        ports:
          - 110
        usernames:
          - root
          - admin
          - user
          - test
        passwords:
          nettacker_fuzzer:
            input_format: '{{passwords}}'
            prefix:
            suffix:
            interceptors:
            data:
              passwords:
                read_from_file: passwords/top_1000_common_passwords.txt
        response:
          condition_type: or
          conditions:
            successful_login:
              regex: ""
              reverse: false