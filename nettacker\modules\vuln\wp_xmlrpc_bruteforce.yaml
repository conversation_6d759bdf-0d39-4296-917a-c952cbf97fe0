info:
  name: wp_xmlrpc_bruteforce_vuln
  author: OWASP Nettacker Team
  severity: 3
  description:
  reference:
  profiles:
    - vuln
    - vulnerability
    - http
    - low_severity
    - wordpress
    - wp

payloads:
  - library: http
    steps:
      - method: post
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
          Content-Type: "text/xml"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/xmlrpc.php"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        data: <methodCall><methodName>wp.getUsersBlogs</methodName><params><param><value><string>admin</string></value></param><param><value><string></string></value></param></params></methodCall>
        response:
          condition_type: or
          conditions:
            content:
              regex: <int>403</int>
              reverse: false
