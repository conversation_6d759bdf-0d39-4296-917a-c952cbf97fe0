---
scan_started: Ο μηχανισμός Nettacker ξεκίνησε ...
options: python nettacker.py [επιλογές]
help_menu: Εμφάνιση του μενού βοήθειας Nettacker
license: Διαβάστε τις άδειες και τις συμφωνίες https://github.com/OWASP/Nettacker
engine: Κινητήρας
engine_input: Επιλογές εισαγωγής κινητήρα
select_language: επιλέξτε μια γλώσσα {0}
range: σάρωση όλων των διευθύνσεων IP στην περιοχή
subdomains: βρείτε και σαρώστε υποτομείς
thread_number_connections: αριθμούς νήματος για συνδέσεις σε έναν κεντρικό υπολογιστή
thread_number_hosts: τους αριθμούς νήματος για τους ξενιστές σάρωσης
save_logs:
  να αποθηκεύσετε όλα τα αρχεία καταγραφής στο αρχείο (results.txt, results.html,
  results.json)
target: Στόχος
target_input: Επιλογές εισαγωγής στόχων
target_list: λίστα στόχων, χωριστά με ","
read_target: να διαβάσετε τους στόχους από το αρχείο
scan_method_options: Επιλογές μεθόδου σάρωσης
choose_scan_method: επιλέξτε τη μέθοδο σάρωσης {0}
exclude_scan_method: επιλέξτε μέθοδο σάρωσης για να εξαιρέσετε {0}
username_list: όνομα χρήστη (ες), χωριστά με ","
username_from_file: να διαβάσετε το όνομα χρήστη από το αρχείο
password_separator: λίστα κωδικών πρόσβασης, ξεχωριστά με ","
read_passwords: διαβάζουν τον κωδικό πρόσβασης από το αρχείο
port_separator: λίστα λιμένων, χωριστά με ","
time_to_sleep: χρόνος για ύπνο μεταξύ κάθε αιτήματος
error_target: Δεν είναι δυνατός ο προσδιορισμός του στόχου
error_target_file:
  "Δεν είναι δυνατός ο προσδιορισμός των στόχων που δεν μπορούν να
  ανοίξουν το αρχείο: {0}"
thread_number_warning:
  είναι καλύτερα να χρησιμοποιήσετε αριθμό νήματος μικρότερο
  από 100, BTW συνεχίζουμε ...
settimeout: ορίστε το χρονικό όριο στα {0} δευτερόλεπτα, είναι πολύ μεγάλο, έτσι
  δεν είναι; από τον τρόπο που συνεχίζουμε ...
scan_module_not_found: αυτή η ενότητα σάρωσης [{0}] δεν βρέθηκε!
error_exclude_all: δεν μπορείτε να εξαιρέσετε όλες τις μεθόδους σάρωσης
exclude_module_error: η {0} ενότητα που επιλέξατε για να εξαιρέσετε δεν βρέθηκε!
method_inputs:
  "εισάγετε μεθόδους εισόδου, για παράδειγμα: ftp_brute_users = test,
  admin & ftp_brute_passwds = read_from_file: /tmp/pass.txt&ftp_brute_port=21"
error_reading_file: δεν μπορεί να διαβάσει το αρχείο {0}
error_username:
  "Δεν είναι δυνατός ο προσδιορισμός των ονομάτων χρηστών που δεν μπορούν
  να ανοίξουν το αρχείο: {0}"
found: "{0} βρέθηκε! ({1}: {2})"
error_password_file:
  "Δεν είναι δυνατός ο προσδιορισμός των κωδικών πρόσβασης, δεν
  είναι δυνατή η ανοίξη αρχείου: {0}"
file_write_error: το αρχείο "{0}" δεν είναι εγγράψιμο!
scan_method_select: επιλέξτε τη μέθοδο σάρωσης!
remove_temp: κατάργηση αρχείων temp!
sorting_results: διαλογή αποτελεσμάτων!
done: Έγινε!
start_attack: να αρχίσετε να επιτεθείτε {0}, {1} από {2}
module_not_available: αυτή η ενότητα "{0}" δεν είναι διαθέσιμη
error_platform:
  δυστυχώς αυτή η έκδοση του λογισμικού θα μπορούσε απλώς να τρέξει
  σε linux / osx / windows.
python_version_error: Η έκδοση Python δεν υποστηρίζεται!
skip_duplicate_target:
  παρακάμπτοντας τον διπλό στόχο (ορισμένοι υποτομείς / τομείς
  μπορούν να έχουν το ίδιο IP και εύρος)
unknown_target: άγνωστος τύπος στόχου [{0}]
checking_range: έλεγχος εύρους {0} ...
checking: έλεγχος {0} ...
HOST: ΠΛΗΘΟΣ
USERNAME: USERNAME
PASSWORD: ΚΩΔΙΚΟΣ ΠΡΟΣΒΑΣΗΣ
PORT: ΛΙΜΑΝΙ
TYPE: ΤΥΠΟΣ
DESCRIPTION: ΠΕΡΙΓΡΑΦΗ
verbose_mode: "(0-5) (προεπιλογή 0)"
software_version: εμφάνιση της έκδοσης λογισμικού
check_updates: Έλεγχος για ενημερώσεις
outgoing_proxy:
  "προεπιλεγμένες συνδέσεις εξερχόμενων συνδέσεων (κάλτσες). παράδειγμα
  socks5: 127.0.0.1:9050, κάλτσες: //127.0.0.1: 9050 socks5: //127.0.0.1: 9050 ή socks4:
  socks4: //127.0.0.1: 9050, έλεγχος ταυτότητας: κάλτσες: // username: password @
  127.0.0.1, socks4: // όνομα χρήστη: password@127.0.0.1, socks5: // username: password@127.0.0.1"
valid_socks_address:
  "εισάγετε την έγκυρη διεύθυνση και τη θύρα των κάλτσων. παράδειγμα
  socks5: 127.0.0.1:9050, κάλτσες: //127.0.0.1: 9050, socks5: //127.0.0.1: 9050 ή
  socks4: socks4: //127.0.0.1: 9050, έλεγχος ταυτότητας: κάλτσες: // username: password
  @ 127.0.0.1, socks4: // όνομα χρήστη: password@127.0.0.1, socks5: // username: password@127.0.0.1"
connection_retries: Επαναλαμβάνει πότε το χρονικό όριο σύνδεσης (προεπιλογή 3)
ftp_connectiontimeout: "Σύνδεση ftp με {0}: {1} λήξη χρόνου, παρακάμπτοντας {2}:
  {3}"
login_successful: ΕΓΓΡΑΦΕΙΤΑΙ ΣΕ ΕΠΙΤΥΧΙΑ!
login_list_error:
  ΠΟΥ ΕΧΕΙ ΕΠΙΤΕΥΧΘΗΚΑΝ ΕΠΙΤΥΧΙΑ, ΑΔΕΙΑ ΑΠΑΓΟΡΕΥΕΤΑΙ ΓΙΑ ΔΕΣΜΕΥΣΗ
  ΚΑΤΑΛΟΓΟΥ!
ftp_connection_failed:
  "Η σύνδεση ftp με {0}: {1} απέτυχε, παραλείποντας ολόκληρο
  το βήμα [διαδικασία {2} της {3}]! πηγαίνετε στο επόμενο βήμα"
input_target_error:
  ο στόχος εισόδου για τη λειτουργική μονάδα {0} πρέπει να είναι
  DOMAIN, HTTP ή SINGLE_IPv4, παρακάμπτοντας {1}
user_pass_found: "χρήστη: {0} περάσει: {1} φιλοξενεί: {2} λιμάνι: {3} βρέθηκε!"
file_listing_error: "(ΧΩΡΙΣ ΑΔΕΙΑ ΓΙΑ ΑΡΧΕΙΑ ΚΑΤΑΛΟΓΟΥ)"
trying_message: "προσπαθώντας {0} από {1} στη διαδικασία {2} από {3} {4}: {5} ({6})"
smtp_connectiontimeout: "σύνδεση smtp με {0}: {1} timeout, παρακάμπτοντας {2}: {3}"
smtp_connection_failed:
  "η σύνδεση smtp με {0}: {1} απέτυχε, παρακάμπτοντας ολόκληρο
  το βήμα [process {2} of {3}]! πηγαίνετε στο επόμενο βήμα"
ssh_connectiontimeout:
  "σύνδεση ssh με {0}: {1} χρονικό όριο, παρακάμπτοντας {2}:
  {3}"
ssh_connection_failed:
  "Η σύνδεση ssh με {0}: {1} απέτυχε, παραλείποντας ολόκληρο
  το βήμα [διαδικασία {2} της {3}]! πηγαίνετε στο επόμενο βήμα"
port/type: "{0} / {1}"
port_found: "host: {0} θύρα: {1} ({2}) βρέθηκε!"
target_submitted: ο στόχος {0} υποβλήθηκε!
current_version:
  εκτελείτε την έκδοση OWASP Nettacker {0} {1} {2} {6} με το όνομα
  κώδικα {3} {4} {5}
feature_unavailable:
  αυτή η λειτουργία δεν είναι ακόμα διαθέσιμη! εκτελέστε τον "git
  clone https://github.com/OWASP/Nettacker.git ή εγκαταστήστε pip -U OWASP-Nettacker
  για να λάβετε την τελευταία έκδοση.
available_graph:
  "να δημιουργήσετε ένα γράφημα όλων των δραστηριοτήτων και πληροφοριών,
  πρέπει να χρησιμοποιήσετε την έξοδο HTML. διαθέσιμα γραφήματα: {0}"
graph_output:
  για να χρησιμοποιήσετε τη γραφική παράσταση, το όνομα αρχείου εξόδου
  σας πρέπει να τελειώνει με ".html" ή ".htm"!
build_graph: γραφικό κτίριο ...
finish_build_graph: φινίρισμα γραφικών!
pentest_graphs: Γραφήματα δοκιμής διείσδυσης
graph_message:
  Αυτό το γράφημα δημιουργήθηκε από το OWASP Nettacker. Το γράφημα περιέχει
  όλες τις δραστηριότητες ενότητες, τον χάρτη δικτύου και τις ευαίσθητες πληροφορίες.
  Μην μοιράζεστε αυτό το αρχείο με κανέναν, αν δεν είναι αξιόπιστο.
nettacker_report: Έκθεση OWASP Nettacker
nettacker_version_details:
  "Λεπτομέρειες λογισμικού: έκδοση OWASP Nettacker {0} [{1}]
  σε {2}"
no_open_ports: δεν βρέθηκαν ανοιχτές θύρες!
no_user_passwords: δεν βρέθηκε χρήστης / κωδικός πρόσβασης!
loaded_modules: Έγινε φόρτωση {0} μονάδων ...
graph_module_404: "αυτό το στοιχείο γραφήματος δεν βρέθηκε: {0}"
graph_module_unavailable: αυτή η ενότητα γραφικών "{0}" δεν είναι διαθέσιμη
ping_before_scan: ping πριν σαρώσετε τον κεντρικό υπολογιστή
skipping_target:
  παρακάμπτοντας ολόκληρο τον στόχο {0} και τη μέθοδο σάρωσης {1} εξαιτίας
  του -ping-before-scan είναι αλήθεια και δεν απάντησε!
not_last_version:
  δεν χρησιμοποιείτε την τελευταία έκδοση του OWASP Nettacker, ενημερώστε
  το.
cannot_update:
  δεν μπορείτε να ελέγξετε για την ενημέρωση, ελέγξτε τη σύνδεσή σας
  στο διαδίκτυο.
last_version: Χρησιμοποιείτε την τελευταία έκδοση του OWASP Nettacker ...
directoy_listing: κατάλογο ευρετηρίου που βρέθηκε στο {0}
insert_port_message:
  εισάγετε τη θύρα μέσω του διακόπτη -g ή -methods-args αντί της
  url
http_connectiontimeout: σύνδεση HTTP {0} timeout!
wizard_mode: έναρξη λειτουργίας οδηγού
directory_file_404: δεν βρέθηκε κατάλογος ή αρχείο για {0} στη θύρα {1}
open_error: δεν μπορεί να ανοίξει {0}
dir_scan_get:
  Η τιμή dir_scan_http_method πρέπει να είναι GET ή HEAD, ορίστε προεπιλογή
  για GET.
list_methods: λίστα όλων των μεθόδων args
module_args_error: δεν μπορεί να πάρει {0} args module
trying_process: προσπαθώντας {0} από {1} στη διαδικασία {2} από {3} στις {4} ({5})
domain_found: "τομέας που βρέθηκε: {0}"
TIME: ΧΡΟΝΟΣ
CATEGORY: ΚΑΤΗΓΟΡΙΑ
module_pattern_404: δεν μπορεί να βρει κάποια υπομονάδα με μοτίβο {0}!
enter_default: πληκτρολογήστε {0} | Προεπιλεγμένο [{1}]>
enter_choices_default: πληκτρολογήστε {0} | επιλογές [{1}] | Προεπιλεγμένο [{2}]>
all_targets: τους στόχους
all_thread_numbers: τον αριθμό του νήματος
out_file: το όνομα αρχείου εξόδου
all_scan_methods: τις μεθόδους σάρωσης
all_scan_methods_exclude: τις μεθόδους σάρωσης για εξαίρεση
all_usernames: τα ονόματα χρηστών
all_passwords: τους κωδικούς πρόσβασης
timeout_seconds: το χρονικό όριο δευτερόλεπτα
all_ports: τους αριθμούς των θυρών
all_verbose_level: το επίπεδο verbose
all_socks_proxy: το πληρεξούσιο κάλτσες
retries_number: τον αριθμό επανάληψης
graph: ένα γράφημα
subdomain_found: "υποτομέας βρέθηκε: {0}"
select_profile: επιλέξτε προφίλ {0}
profile_404: το προφίλ "{0}" δεν βρέθηκε!
waiting: περιμένοντας {0}
vulnerable: ευάλωτο σε {0}
target_vulnerable: "στόχος {0}: {1} είναι ευάλωτος σε {2}!"
no_vulnerability_found: καμία ευπάθεια δεν βρέθηκε! ({0})
Method: Μέθοδος
API: API
API_options: Επιλογές API
start_api_server: εκκίνηση της υπηρεσίας API
API_host: Διεύθυνση κεντρικού υπολογιστή API
API_port: Αριθμός θύρας API
API_debug: Λειτουργία εντοπισμού σφαλμάτων API
API_access_key: Πλήκτρο πρόσβασης API
white_list_API: απλά επιτρέψτε στους οικοδεσπότες λευκής λίστας να συνδεθούν στο API
define_white_list:
  "καθορίστε ξένους καταλόγους φιλοξενίας, ξεχωριστοί με, (παραδείγματα:
  127.0.0.1, ***********/24, ********-**********)"
gen_API_access_log: Δημιουργία αρχείου καταγραφής πρόσβασης API
API_access_log_file: Όνομα αρχείου καταγραφής πρόσβασης API
API_port_int: Η θύρα API πρέπει να είναι ένας ακέραιος αριθμός!
unknown_ip_input:
  άγνωστος τύπος εισόδου, οι αποδεκτοί τύποι είναι SINGLE_IPv4, RANGE_IPv4,
  CIDR_IPv4
API_key: "* Κλειδί API: {0}"
ports_int: οι θύρες πρέπει να είναι ακέραιοι! (π.χ. 80 | 80,1080 | 80,1080-1300,9000,12000-15000)
through_API: Μέσω του API του OWASP Nettacker
API_invalid: μη έγκυρο κλειδί API
unauthorized_IP: η IP σας δεν είναι εξουσιοδοτημένη
not_found: Δεν βρέθηκε!
no_subdomain_found: "subdomain_scan: δεν βρέθηκε υποτομέας!"
viewdns_domain_404: "viewdns_reverse_ip_lookup_scan: δεν βρέθηκε κανένα πεδίο!"
browser_session_valid: η περίοδος λειτουργίας του προγράμματος περιήγησης είναι έγκυρη
browser_session_killed: η σύνοδος του προγράμματος περιήγησης που σκοτώθηκε
updating_database: ενημέρωση της βάσης δεδομένων ...
database_connect_fail: δεν μπόρεσε να συνδεθεί στη βάση δεδομένων!
inserting_report_db: εισάγοντας αναφορά στη βάση δεδομένων
inserting_logs_db: εισάγοντας αρχεία καταγραφής στη βάση δεδομένων
removing_logs_db: καταργώντας παλιά αρχεία καταγραφής από db
len_subdomain_found: "{0} βρέθηκαν υποτομέας!"
len_domain_found: Ο {0} τομέας βρέθηκε!
phpmyadmin_dir_404: δεν βρέθηκε κανένα phpmyadmin dir!
DOS_send: αποστολή πακέτων DoS σε {0}
host_up: "{0} είναι επάνω! Ο χρόνος που απαιτείται για να επιστρέψετε είναι {1}"
host_down: Δεν μπορεί να γίνει ping {0}!
root_required: αυτό πρέπει να τρέξει ως ρίζα
admin_scan_get:
  Η τιμή admin_scan_http_method πρέπει να είναι GET ή HEAD, ορίστε προεπιλογή
  για GET.
telnet_connectiontimeout:
  "σύνδεση telnet με {0}: {1} χρονικό όριο, παρακάμπτοντας
  {2}: {3}"
telnet_connection_failed:
  "Η σύνδεση telnet με {0}: {1} απέτυχε, παραλείποντας ολόκληρο
  το βήμα [διαδικασία {2} της {3}]! πηγαίνετε στο επόμενο βήμα"
http_auth_success:
  "http βασική επιτυχία ελέγχου ταυτότητας - κεντρικός υπολογιστής:
  {2}: {3}, χρήστης: {0}, περάσει: {1} βρέθηκε!"
http_auth_failed:
  "Ο βασικός έλεγχος ταυτότητας http απέτυχε να {0}: {3} χρησιμοποιώντας
  {1}: {2}"
http_form_auth_success:
  "http επιτυχία πιστοποίησης επιτυχίας - host: {2}: {3}, χρήστης:
  {0}, περάσει: {1} βρέθηκε!"
http_form_auth_failed:
  "Ο έλεγχος ταυτότητας http απέτυχε να {0}: {3} χρησιμοποιώντας
  {1}: {2}"
http_ntlm_success:
  "http ntlm επιτυχία ελέγχου ταυτότητας - κεντρικός υπολογιστής:
  {2}: {3}, χρήστης: {0}, περάσει: {1} βρέθηκε!"
http_ntlm_failed:
  "Ο έλεγχος ταυτότητας http ntlm απέτυχε να {0}: {3} χρησιμοποιώντας
  {1}: {2}"
no_response: δεν μπορεί να πάρει απάντηση από τον στόχο
category_framework: "κατηγορία: {0}, πλαίσια: {1} βρέθηκαν!"
nothing_found: τίποτα δεν βρέθηκε στο {0} στο {1}!
no_auth: "Δεν βρέθηκε auth στο {0}: {1}"
