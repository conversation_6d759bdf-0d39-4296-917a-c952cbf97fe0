info:
  name: wp_xmlrpc_pingback_vuln
  author: OWASP Nettacker Team
  severity: 3
  description:
  reference:
  profiles:
    - vuln
    - vulnerability
    - http
    - wordpress
    - wp

payloads:
  - library: http
    steps:
      - method: post
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
          Content-Type: "text/xml"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/xmlrpc.php"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        data: <methodCall><methodName>pingback.ping</methodName><params><param><value><string>http://Cannotbehere:22/</string></value></param><param><value><string>{target}</string></value></param></params></methodCall>
        response:
          condition_type: or
          conditions:
            content:
              regex: <name>16</name>
              reverse: false
