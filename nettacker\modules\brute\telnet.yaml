info:
  name: telnet_brute
  author: OWASP Nettacker Team
  severity: 3
  description: Telnet Bruteforcer
  reference:
  profiles:
    - brute
    - brute_force
    - telnet

payloads:
  - library: telnet
    steps:
      - method: brute_force
        timeout: 3
        host: "{target}"
        ports:
          - 23
        usernames:
          - root
          - admin
          - user
          - test
        passwords:
          nettacker_fuzzer:
            input_format: '{{passwords}}'
            prefix:
            suffix:
            interceptors:
            data:
              passwords:
                read_from_file: passwords/top_1000_common_passwords.txt
        response:
          condition_type: or
          conditions:
            successful_login:
              regex: ""
              reverse: false

