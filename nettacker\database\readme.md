OWASP Nettacker Database Files
=======================
This folder mainly contains all the files which handle the database transactions for the OWASP Nettacker.

* `db.py` contains the database transaction functions
* `models.py` contains the database structure layout
* `mysql_create.py` contains functions to create the db structure mentioned in `models.py` into a MySQL database
* `sqlite_create.py` contains functions to create the db structure mentioned in `models.py` into a SQLite database
