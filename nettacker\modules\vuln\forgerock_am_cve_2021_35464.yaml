info:
  name: forgerock_am_cve_2021_35464_vuln
  author: OWASP Nettacker Team
  severity: 9
  description: ForgeRock AM server before 7.0 has a Java deserialization vulnerability in the jato.pageSession parameter on multiple pages. The exploitation does not require authentication, and remote code execution can be triggered by sending a single crafted /ccversion/* request to the server. The vulnerability exists due to the usage of Sun ONE Application Framework (JATO) found in versions of Java 8 or earlier
  reference: 
    - https://portswigger.net/research/pre-auth-rce-in-forgerock-openam-cve-2021-35464
  profiles:
    - vuln
    - vulnerability
    - http
    - critical_severity
    - cve2021
    - cve
    - rce
    - openam
    - forgerock_am

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/openam/oauth2/..;/ccversion/Version"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443

        response:
          condition_type: and
          conditions:
            status_code:
              regex: '200'
              reverse: false
            content:
              regex: "Version Information -|openam/ccversion/Masthead.jsp"
              reverse: false
            headers:
              Set-Cookie:
                regex: JSESSIONID=
                reverse: false
