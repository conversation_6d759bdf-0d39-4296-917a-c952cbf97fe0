[tool.poetry]
name = "nettacker"
version = "0.4.0"
description = "Automates information gathering, vulnerability scanning and aids penetration testing engagements in general"
license = "Apache-2.0"
readme = "README.md"

authors = ["OWASP Nettacker Contributors"]
classifiers = [
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
keywords = [
    "automation",
    "bruteforce",
    "cve",
    "hacking-tools",
    "information-gathering",
    "network-analysis",
    "owasp",
    "penetration-testing",
    "pentesting",
    "pentesting-tools",
    "port-scanner",
    "python",
    "security-tools",
    "security",
    "vulnerability-management",
    "vulnerability-scanner",
]

homepage = "https://owasp.org/www-project-nettacker"
repository = "https://github.com/OWASP/Nettacker"
documentation = "https://github.com/OWASP/Nettacker/wiki"

packages = [{ include = "nettacker"}]

[tool.nettacker]
release_name = "QUIN"

[tool.poetry.scripts]
nettacker = "nettacker.main:run"

[tool.poetry.dependencies]
python = "^3.9, <3.13"
aiohttp = "^3.9.5"
argparse = "^1.4.0"
asyncio = "^3.4.3"
flask = "^3.0.1"
ipaddr = "^2.2.0"
multiprocess = "^0.70.15"
netaddr = "^0.9.0"
numpy = ">=1.26.1,<3.0.0"
paramiko = "^3.4.0"
py3dns = "^4.0.0"
pyopenssl = "^23.2.0"
pysocks = "^1.7.1"
pyyaml = "^6.0.1"
requests = "^2.32.3"
sqlalchemy = "^2.0.22"
texttable = "^1.7.0"
zipp = "^3.19.1"

[tool.poetry.group.dev.dependencies]
ipython = "^8.16.1"
ruff = ">=0.2.1,<0.7.0"

[tool.poetry.group.test.dependencies]
coverage = "^7.3.2"
pytest = ">=7.4.3,<9.0.0"
pytest-cov = ">=4.1,<6.0"
pytest-xdist = "^3.3.1"

[tool.poetry.urls]
"Sponsor" = "https://owasp.org/donate/?reponame=www-project-nettacker&title=OWASP+Nettacker"

[tool.coverage.run]
branch = true

[tool.isort]
known_first_party = ["nettacker", "tests"]
line_length = 99
multi_line_output = 3
no_inline_sort = true
profile = "black"

[tool.pytest.ini_options]
addopts = "--cov=nettacker --cov-config=pyproject.toml --cov-report term --cov-report xml --dist loadscope --no-cov-on-fail --numprocesses auto"
testpaths = ["tests"]

[tool.ruff]
line-length = 99

# [tool.ruff.lint]
# select = ["E4", "E7", "E9", "F", "T"]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
