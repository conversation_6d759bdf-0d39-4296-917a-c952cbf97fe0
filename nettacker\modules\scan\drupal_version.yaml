info:
  name: drupal_version_scan
  author: OWASP Nettacker Team
  severity: 3
  description: fetch drupal version from target
  reference:
  profiles:
    - scan
    - http
    - backup
    - low_severity
    - drupal

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/CHANGELOG.txt"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: and        
          conditions:
            status_code:
                regex: "200"
                reverse: false
            content:
              regex: ^Drupal (\d+\.\d+)\,
              reverse: false
          log: "response_dependent['content']"
