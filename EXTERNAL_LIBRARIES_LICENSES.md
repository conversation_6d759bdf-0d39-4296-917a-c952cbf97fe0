* `argparse`: argparse is licensed under the Python license
* `netaddr`: BSD License
* `requests`: Apache2 License
* `paramiko`: GPL v2.1
* `texttable`: GPL v3.0
* `PySocks`: BSD
* `win_inet_pton`: This software released into the public domain. Anyone is free to copy, modify, publish, use, compile, sell, or distribute this software, either in source code form or as a compiled binary, for any purpose, commercial or non-commercial, and by any means.
* `pyOpenSSL`: Apache License 2.0
* `flask`: Flask is licensed under a three clause BSD License. It basically means: do whatever you want with it as long as the copyright in Flask sticks around, the conditions are not modified and the disclaimer is present. Furthermore you must not use the names of the authors to promote derivatives of the software without written consent.
* `lockfile`: MIT License
* `bs4`: MIT
* `pycurl`: PycURL is dual licensed under the LGPL and an MIT/X derivative license based on the libcurl license. The complete text of the licenses is available in COPYING-LGPL and COPYING-MIT files in the source distribution.
* `pyspf`: Python Software Foundation License (Python Software Foundation License)
* `OWASP ZSC`: GPL v2
* `icmp`: https://gist.github.com/pklaus/856268, https://github.com/corentone/python3-ping/blob/master/ping.py
* `fontawesome`: https://fontawesome.com/license
* `glyphicons`: https://glyphicons.com/license/ 
* `bootstrap`: https://getbootstrap.com/docs/4.0/about/license/
* `jquery`: https://jquery.org/license/
* `angularjs`: MIT License
* `buttons`: MIT License
* `d3js`: BSD 3-Clause "New" or "Revised" License
* `animate`: MIT License
* `flag-icon`: MIT License
* `jit`: https://github.com/philogb/jit/blob/master/LICENSE
* `sqlalchemy`: MIT License
* `canari`: GNU General Public License v3.0
* `intro.js`: GNU AGPLv3
