info:
  name: confluence_version_scan
  author: <PERSON> Ly
  severity: 3
  description: Fetch Confluence version from target
  reference:
  profiles:
    - scan
    - http
    - backup
    - low_severity
    - confluence
    - atlassian

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/dashboard.action"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: or
          conditions:
            content:
              regex: <span id=\'footer-build-information\'>(.+?)</span>
              reverse: false 
          log: "response_dependent['content']"