info:
  name: pop3_brute
  author: OWASP Nettacker Team
  severity: 3
  description: POP3 SSL Bruteforcer
  reference:
  profiles:
    - brute
    - brute_force
    - pop3

payloads:
  - library: pop3s
    steps:
      - method: brute_force
        timeout: 3
        host: "{target}"
        ports:
          - 995
        usernames:
          - root
          - admin
          - user
          - test
        passwords:
          nettacker_fuzzer:
            input_format: '{{passwords}}'
            prefix:
            suffix:
            interceptors:
            data:
              passwords:
                read_from_file: passwords/top_1000_common_passwords.txt
        response:
          condition_type: or
          conditions:
            successful_login:
              regex: ""
              reverse: false