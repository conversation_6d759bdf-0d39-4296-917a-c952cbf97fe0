info:
  name: xdebug_rce_vuln
  author: OWASP Nettacker Team
  severity: 10
  description:
  reference:
  profiles:
    - vuln
    - vulnerability
    - http
    - critical_severity
    - rce

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: or
          conditions:
            headers:
              Xdebug:
                regex: "2.5.5"
                reverse: false
