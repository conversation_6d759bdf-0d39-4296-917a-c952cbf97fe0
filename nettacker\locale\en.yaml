API: API
API_access_key: API access key
API_access_log_file: API access log filename
API_cert: API CERTIFICATE
API_cert_key: API CERTIFICATE Key
API_debug: API debug mode
API_host: API host address
API_invalid: invalid API key
API_key: " * API is accessible from https://nettacker-api.z3r0d4y.com:{0}/ via API Key: {1}"
API_options: API options
API_port: API port number
Method: Method
skip_service_discovery: skip service discovery before scan and enforce all modules to scan anyway
no_live_service_found: no any live service found to scan.
icmp_need_root_access: to use icmp_scan module or --ping-before-scan you need to run the script as root!
available_graph: "build a graph of all activities and information, you must use HTML output. available graphs: {0}"
browser_session_killed: your browser session killed
browser_session_valid: your browser session is valid
build_graph: building graph ...
connection_retries: Retries when the connection timeout (default 3)
current_version: you are running OWASP Nettacker version {0}{1}{2}{6} with code name {3}{4}{5}
database_connect_fail: could not connect to the database!
database_connection_failed: Connection to the selected db failed
define_white_list: "define white list hosts, separate with , (examples: 127.0.0.1, ***********/24, ********-**********)"
engine: Engine
filtered_content: ... [see the full content in the report]
engine_input: Engine input options
Invalid_whatcms_api_key: "{0}"
searching_whatcms_database: Searching for CMS on whatcms.org...
whatcms_monthly_quota_exceeded: You have exceeded your monthly WHATCMS request quota
finished_module: finished module {0} towards the target {1} | module thread number {2} from {3}!
modules_extra_args_help: add extra args to pass to modules (e.g. --modules-extra-args "x_api_key=123&xyz_passwd=abc"
choose_scan_method: choose modules {0} to see full list use --show-all-modules
cannot_run_api_server: You can't run API Server through itself!
error_target: Cannot specify the target(s)
error_target_file: "Cannot specify the target(s), unable to open file: {0}"
error_username: "Cannot specify the username(s), unable to open file: {0}"
exclude_scan_method: choose scan method to exclude {0}
file_write_error: file "{0}" is not writable!
library_not_supported: library [{0}] is not support!
removing_old_db_records: Removing old database record for selected targets and modules.
regrouping_targets: regrouping targets based on hardware resources!
finish_build_graph: finish building graph!
finished_parallel_module_scan: process-{0}|{1}|{2}| finished module thread number {3} from {4}
graph_message: "This graph created by OWASP Nettacker. Graph contains all modules activities, network map and
sensitive information, Please don't share this file with anyone if it's not reliable."
graph_module_404: "this graph module not found: {0}"
graph_module_unavailable: this graph module "{0}" is not available
graph_output: to use graph feature your output filename must end with ".html" or ".htm"!
help_menu: Show Nettacker Help Menu
done: "done!"
error_platform: "Unfortunately, this version of the software can run on Linux/darwin"
file_saved: "report saved in {0} and database"
inserting_report_db: inserting report to the database
invalid_database: Please select from mysql or sqlite in the configuration file
invalid_json_type_to_db: "Invalid type of JSON data for the database. Skipping the submission to database. Data:{0}"
license: "Please read license and agreements https://github.com/OWASP/Nettacker"
loaded_modules: "{0} modules loaded ..."
loading_modules: loading all modules... it might get some time!
loading_profiles: loading all profiles... it might get some time!
module_profile_full_information: "{0}{1}{2}: {3}"
nettacker_report: OWASP Nettacker Report
nettacker_version_details: "Software Details: OWASP Nettacker version {0} [{1}] in {2}"
not_found: Not Found!
outgoing_proxy: "outgoing connections proxy (socks). example socks5: 127.0.0.1:9050,
  socks://127.0.0.1:9050 socks5://127.0.0.1:9050 or socks4: socks4://127.0.0.1:9050,
  authentication: socks://username: password@127.0.0.1, socks4://username:password@127.0.0.1,
  socks5://username:password@127.0.0.1"
password_separator: password(s) list, separate with ","
pentest_graphs: Penetration Testing Graphs
ping_before_scan: ping before scan the host
port_separator: port(s) list, separate with ","
ports_int: ports must be integers! (e.g. 80 || 80,1080 || 80,1080-1300,9000,12000-15000)
profile_404: the profile "{0}" not found!
range: scan all IPs in the range
read_passwords: read password(s) from file
read_target: read target(s) from file
removing_logs_db: removing old logs from db
save_logs: save all logs in file (results.txt, results.csv, results.html, results.json)
scan_method_options: Scan method options
scan_method_select: please choose your scan method!
scan_module_not_found: this scan module [{0}] not found!
scan_started: "Nettacker engine started ..."
select_language: select a language {0}
select_profile: select profile {0}
select_user_agent: "Select a user agent to send with HTTP requests or enter \"random_user_agent\"
  to randomize the User-Agent in the requests."
send_success_event_from_module: "process-{0}|{1}|{2}|module-thread {3}/{4}|request-thread {5}/{6}|{7}|\nsuccess_condition
(s): \n{8}"
send_unsuccess_event_from_module: "process-{0}|{1}|{2}|module-thread {3}/{4}|request-thread
  {5}/{6}| all conditions failed"
sending_module_request: "process-{0}|{1}|{2}|module-thread {3}/{4}| sending request
  {5} from {6}"
verbose_event: enable verbose event to see state of each thread
no_events_for_report: there are no events exist to create a report! skipping this section.
set_hardware_usage: Set hardware usage while scanning. (low, normal, high, maximum)
show_all_modules: show all modules and their information
show_all_profiles: show all profiles and their information
single_process_started: process-{0}| process is started!
software_version: show software version
start_api_server: start the API service
start_multi_process: imported {0} targets in {1} process(es).
start_parallel_module_scan:
  process-{0}|{1}|{2}| started module thread number {3}
  from {4}
subdomains: find and scan subdomains
target: Target
target_input: Target input options
target_list: target(s) list, separate with ","
thread_number_connections: thread numbers for connections to a host
thread_number_modules: parallel module scan for hosts
time_to_sleep: time to sleep between each request
unauthorized_IP: your IP not authorized
updating_database: updating the database...
username_from_file: read username(s) from file
username_list: username(s) list, separate with ","
verbose_mode: verbose mode level (0-5) (default 0)
wrong_hardware_usage: "You must select one of these profiles for hardware usage. (low, normal, high, maximum)"
invalid_scan_id: your scan id is not valid!
compare_scans: compare current scan to old scans using the unique scan_id
compare_report_path_filename: the file-path to store the compare_scan report
no_scan_to_compare: the scan_id to be compared not found
compare_report_saved: "compare results saved in {0}"
build_compare_report: "building compare report"
finish_build_report: "Finished building compare report"
