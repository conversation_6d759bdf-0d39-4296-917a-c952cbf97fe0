info:
  name: ssl_self_signed_certificate_vuln
  author: Captain-T2004
  severity: 6
  description: check if the ssl certificate is self-signed
  reference:
    - https://www.ssl.com/article/ssl-tls-self-signed-certificates/
  profiles:
    - scan
    - ssl

payloads:
  - library: ssl
    steps:
      - method: ssl_certificate_scan
        timeout: 3
        host: "{target}"
        ports:
          - 21
          - 25
          - 110
          - 143
          - 443
          - 587
          - 990
          - 1080
          - 8080
        response:
          condition_type: or
          conditions:
            grouped_conditions:
              condition_type: and
              conditions:
                self_signed:
                  reverse: false
                issuer:
                  reverse: false
                subject:
                  reverse: false