body {
    background: url("/img/background.jpeg")
}

#new_scan, #home, #get_results,#crawler_area,#compare_area{
    background: #FEFCFF;
    padding: 20px;
    border-radius: 5px;
}

/*@import url(http://fonts.googleapis.com/css?family=Lato:100,300,400,700,900);*/
[am-LatoSans] {
    font-family: 'Lato', sans-serif;
}

[am-TopLogo] {
    max-height: 70px;
    max-width: 210px;
    margin: 12px 11px;
}

[am-CallNow] {
    font-weight: 200;
    color: #333;
    vertical-align: middle;
    line-height: 35px;
    font-size: 19px;
    padding-right: 8px;
}

/*
Relevant styles below
*/
.topper {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.navbar.navbar-inverse {
    background-image: linear-gradient(#9f9f9f, #535353 3%, #1f1f1f 17%, #212121 49%, #191919 89%, #000000 100%);
    border-top: 1px inset rgba(255, 255, 255, 0.1);
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    margin-top: 10px;
}

.navbar .navbar-nav > li > a {
    color: #d1d1d1;
    font-weight: 700;
    text-rendering: optimizeLegibility;
    text-shadow: 0px -1px black, 0px 1px rgba(255, 255, 255, 0.25);
    line-height: 18px;
}

.navbar .navbar-nav > li.active {
    color: #f8f8f8;
    background-color: #080808;
    box-shadow: inset 0px -28px 23px -21px rgba(255, 255, 255, 0.15);
    border-left: 1px solid #2A2A2A;
    border-right: 1px solid #272727;
}

.btn.btn-gradient-blue {
    background-color: #0c5497 !important;
    background-image: -webkit-linear-gradient(top, #127bde 0%, #072d50 100%);
    background-image: -o-linear-gradient(top, #127bde 0%, #072d50 100%);
    background-image: linear-gradient(to bottom, #127bde 0%, #072d50 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff127bde', endColorstr='#ff072d50', GradientType=0);
    border-color: #072d50 #072d50 #0c5497;
    color: #fff !important;
    text-shadow: 0 -1px 0 rgba(31, 31, 31, 0.29);
    -webkit-font-smoothing: antialiased;
}

#rtlogo {
    max-width: 30%;
    max-height: 30%;
    display: block;
    margin-left: auto;
    margin-right: auto;
    transform-origin: 50% 50%;
    animation: 20s rotateRight infinite linear;
}

@keyframes rotateRight {
    100% {
        transform: rotate(360deg);
    }
}

.lb-sm {
    font-size: 24px;
}

.lb-md {
    font-size: 32px;
}

.lb-lg {
    font-size: 40px;
}

.list-group-item{
    overflow: scroll;
}

.label{
    display: inline-block !important;
    padding: 0.6em .5em 0.6em !important;
    margin-bottom: 5px;
    font-size: 78% !important; 
}

.navbar-btn{
    margin-left: 15px;
}
#api_key_form{
    margin: 0;
}

#refresh_btn_page{
    margin-top:5px;
    margin-left:0;
}

#refresh_btn_update{
    margin-top:5px;
    margin-left:0;
}

#crw_refresh_btn_update{
    margin-top:5px;
    margin-left:0;
}

#crw_refresh_btn_page{
    margin-top:5px;
    margin-left:0;
}

#crawler-form{
    margin-top: 5px;
    margin-left: 0;
    padding: 0;
}

#refresh_btn{
    margin-bottom: 15px;
}

#search_btn{
    margin-top: 5px;
    margin-left:15px;
}

.card-date{
    margin-left: 15px;
}

.card-hr{
    margin-top: 0;
}

.card-flag{
    margin-left: 10px;
}

#pagination_controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

#pagination_controls button {
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  color: #333;
  padding: 6px 12px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
}

#pagination_controls button:hover {
  background-color: #e2e6ea;
}

#pagination_controls button.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

#pagination_controls button#previous_btn,
#pagination_controls button#next_btn {
  background-color: #ffffff;
  border: 1px solid #ccc;
}

#pagination_controls button#previous_btn:disabled,
#pagination_controls button#next_btn:disabled {
  color: #ccc;
  cursor: not-allowed;
}

#pagination_controls button#previous_btn[disabled],
#pagination_controls button#next_btn[disabled] {
  display: none;
}

#pagination_controls {
  display: flex;
  justify-content: center;
}

.page_number_btn {
  margin: 0 5px;
  padding: 5px 10px;
  border: 1px solid #ccc;
  background-color: #f9f9f9;
  cursor: pointer;
}

.page_number_btn.active {
  background-color: #007bff;
  color: white;
}

.page_number_btn:hover:not(.active) {
  background-color: #e9ecef;
}
