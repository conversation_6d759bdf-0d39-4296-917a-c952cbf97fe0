info:
  name: subdomain_scan
  author: OWASP Nettacker Team
  severity: 0
  description: Find subdomains using different sources on internet
  reference:
  profiles:
    - scan
    - information_gathering
    - infortmation
    - info
    - low_severity

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url: "https://jldc.me/anubis/subdomains/{target}"
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "\"([a-zA-Z0-9\\-\\_.\\s]+)\""
              reverse: false
      - method: post
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url: "https://api.certspotter.com/v1/issuances?domain={target}&expand=dns_names&expand=issuer"
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "\"([a-zA-Z0-9\\-\\_.\\s]+.{target})\""
              reverse: false
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url: "https://api.subdomain.center/?domain={target}"
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "\"([a-zA-Z0-9\\-\\_.\\s]+.{target})\""
              reverse: false

      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url: "https://urlscan.io/api/v1/search/?q=domain:{target}"
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "\"([a-zA-Z0-9\\-\\_.\\s]+.{target})\""
              reverse: false

      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url: "https://app.netlas.io/api/domains/?q=*.{target}"
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "[a-zA-Z0-9\\-\\_.\\s]+\\.{target}"
              reverse: false

      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url: "https://otx.alienvault.com/api/v1/indicator/domain/{target}/passive_dns"
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "[a-zA-Z0-9\\-\\_.\\s]+\\.{target}"
              reverse: false

      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url: "https://api.hackertarget.com/hostsearch/?q={target}"
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "[a-zA-Z0-9\\-\\_.\\s]+\\.{target}"
              reverse: false

      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url: "https://crt.sh/?q=%.{target}"
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "[a-zA-Z0-9\\-\\_\\s]+[\\.]+[a-zA-Z0-9\\-\\_\\s]+\\.{target}"
              reverse: false
      - method: post
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url: "https://hackertarget.com/find-dns-host-records/"
        data:
          theinput: "{target}"
          thetest: "hostsearch"
          name_of_nonce_field: "b4b6cc419d"
          "_wp_http_referer": "/find-dns-host-records/"
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "[a-zA-Z0-9\\-\\_.]+\\.{target}"
              reverse: false

      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url: "https://dnsdumpster.com/"
        response:
          save_to_temp_events_only: "dnsdumpster_csrf_token"
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "([a-zA-Z0-9]+\\w{{63}})"
              reverse: false
            headers:
              Set-Cookie:
                regex: ".*"
                reverse: false

      - method: post
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
          Referer: "https://dnsdumpster.com/"
          Cookie: "dependent_on_temp_event[0]['headers']['Set-Cookie'][1]"
        ssl: false
        url: "https://dnsdumpster.com/"
        data:
          csrfmiddlewaretoken: "dependent_on_temp_event[0]['content'][0]"
          targetip: "{target}"
          user: "free"
        response:
          dependent_on_temp_event: "dnsdumpster_csrf_token"
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "[a-zA-Z0-9\\-\\_.]+\\.{target}"
              reverse: false

      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        ssl: false
        url: "https://toolbox.googleapps.com/apps/dig/#ANY/"
        response:
          save_to_temp_events_only: "googledig_csrf_token"
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "([a-zA-Z0-9]+\\w{{63}})"
              reverse: false
            headers:
              Set-Cookie:
                regex: ".*"
                reverse: false

      - method: post
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
          Referer: "https://toolbox.googleapps.com/apps/dig/"
          Cookie: "dependent_on_temp_event[0]['headers']['Set-Cookie'][1]"
          authority: "toolbox.googleapps.com"
          dnt: "1"
          sec-ch-ua-mobile: "?1"
          origin: "https://toolbox.googleapps.com"
          sec-fetch-site: "same-origin"
          sec-fetch-mode: "cors"
          sec-fetch-dest: "empty"
          accept-language: "en-US,en;q=0.9,fa-IR;q=0.8,fa;q=0.7"
        ssl: false
        url: "https://toolbox.googleapps.com/apps/dig/lookup"
        data:
          csrfmiddlewaretoken: "dependent_on_temp_event[0]['content'][0]"
          domain: "{target}"
          typ: "ANY"
        response:
          dependent_on_temp_event: "googledig_csrf_token"
          condition_type: and
          conditions:
            status_code:
              regex: "^[2-3][0-9][0-9]"
              reverse: false
            content:
              regex: "[a-zA-Z0-9\\-\\_.]+\\.{target}"
              reverse: false



# todo: later
#      "subdomain_scan_use_netcraft": [ "True" ],
#      "subdomain_scan_use_ptrarchive": [ "True" ],
#      "subdomain_scan_use_censys": [ "True" ],

