info:
  name: graphql_vuln
  author: OWASP Nettacker Team
  severity: 3
  description:
  reference:
  profiles:
    - vuln
    - information_gathering
    - http
    - low_severity
    - graphql

payloads:
  - library: http
    steps:
      - method: post
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/{{endpoint}}"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
              endpoint:
                - 1239b01720/graphql
        json:
          query: "
          {{
              __schema {{
                  types {{
                  name
                  }}
              }}
              }}
          "
          variables: "{{}}"
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "200"
              reverse: false
            content:
              regex: data|errors
              reverse: false
