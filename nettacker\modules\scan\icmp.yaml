info:
  name: icmp_scan
  author: OWASP Nettacker Team
  severity: 0
  description: check if host is alive through ICMP
  reference:
  profiles:
    - scan
    - information_gathering
    - infortmation
    - info
    - low_severity

payloads:
  - library: socket
    steps:
      - method: socket_icmp
        timeout: 3
        host: "{target}"
        response:
          condition_type: or
          conditions:
            time_response:
              regex: ""
              reverse: false