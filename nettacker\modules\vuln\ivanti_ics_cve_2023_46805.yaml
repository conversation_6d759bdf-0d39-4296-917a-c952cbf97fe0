info:
  name: ivant<PERSON>_ics_cve_2023_46805_vuln
  author: <PERSON>
  severity: 8.2
  description: CVE-2023-46805 is an authentication bypass that is usually chained with CVE-2024-21887 to perform remote code execution on Ivanti ICS 9.x, 22.x. This module checks whether the mitigations have been applied for CVE-2023-46805. 
  reference: 
    - https://forums.ivanti.com/s/article/CVE-2023-46805-Authentication-Bypass-CVE-2024-21887-Command-Injection-for-<PERSON><PERSON>-Connect-Secure-and-Ivanti-Policy-Secure-Gateways?language=en_US
    - https://labs.watchtowr.com/welcome-to-2024-the-sslvpn-chaos-continues-ivanti-cve-2023-46805-cve-2024-21887
  profiles:
    - vuln
    - vulnerability
    - http
    - high_severity
    - cve
    - ivanti
    - ivanti_connect_secure
    - ivanti_ics

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/{{paths}}"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              paths:  
                - "api/v1/configuration/users/user-roles/user-role/rest-userrole1/web/web-bookmarks/bookmark"
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: and
          conditions:
            status_code:
              regex: "403"
              reverse: false
            content:
              regex: "<html>"
              reverse: true
