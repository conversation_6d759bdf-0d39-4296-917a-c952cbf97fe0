name: CI/CD

on:
  pull_request:
  push:
    tags:
      - '*'
  workflow_dispatch:

concurrency:
  cancel-in-progress: true
  group: ${{ github.repository }}-${{ github.workflow }}-${{ github.head_ref || github.ref_name }}

jobs:
  # Code quality checks.
  pre-commit:
    name: Run pre-commit
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: Run pre-commit
        uses: pre-commit/action@v3.0.1

  code-ql:
    name: CodeQL
    needs:
      - pre-commit
    permissions:
      security-events: write
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        language:
          - javascript
          - python
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: /language:${{ matrix.language }}

  # Code tests.
  run-tests:
    name: Run tests
    needs:
      - pre-commit
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade poetry
          poetry install --with test

      - name: Run tests
        run: |
          poetry run pytest

  build-package:
    name: Build package
    needs:
      - run-tests
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip poetry
          poetry install

      - name: Build package
        run: |
          poetry build --no-interaction

      - name: Upload package artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist

  test-build-package:
    name: Test build on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    needs: build-package
    strategy:
      matrix:
        os:
          - macos-latest
          - ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Get package artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist

      - name: Run tests
        shell: bash
        run: |
          rm -rf nettacker
          python -m pip install dist/*.whl
          nettacker --version
          python -m pip uninstall -y nettacker
          python -m pip install dist/*.tar.gz
          nettacker --version

  # Docker related jobs.
  test-docker-image:
    name: Test Docker image
    needs:
      - run-tests
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Build Docker image
        run: docker build . -t nettacker

      - name: Test help menu
        run: |
          docker run -e github_ci=true --rm nettacker \
            poetry run python nettacker.py --help

      - name: Test help menu in Persian
        run: |
          docker run -e github_ci=true --rm nettacker \
            poetry run python nettacker.py --help -L fa

      - name: Show all modules
        run: |
          docker run -e github_ci=true --rm nettacker \
            poetry run python nettacker.py --show-all-modules

      - name: Show all profiles
        run: |
          docker run -e github_ci=true --rm nettacker \
            poetry run python nettacker.py --show-all-profiles

      - name: Test all modules command + check if it's finish successfully + csv
        run: |
          docker run -e github_ci=true --rm -i nettacker \
            poetry run python nettacker.py -i 127.0.0.1 -u user1,user2 -p pass1,pass2 -m all -g 21,25,80,443 \
              -t 1000 -T 3 -o out.csv

      - name: Test all modules command + check if it's finish successfully + csv
        run: |
          docker run -e github_ci=true --rm -i nettacker \
            poetry run python nettacker.py -i 127.0.0.1 -u user1,user2 -p pass1,pass2 -m all -g 21,25,80,443 \
              -t 1000 -T 3 -o out.csv --skip-service-discovery

      - name: Test all modules command + check if it's finish successfully + with graph + Persian
        run: |
          docker run -e github_ci=true --rm -i nettacker \
            poetry run python nettacker.py -i 127.0.0.1 -L fa -u user1,user2 -p pass1,pass2 --profile all \
              -g 21,25,80,443 -t 1000 -T 3 --graph d3_tree_v2_graph -v

      - name: Test all modules command + check if it's finish successfully + with graph + Persian
        run: |
          docker run -e github_ci=true --rm -i nettacker \
            poetry run python nettacker.py -i 127.0.0.1 -L fa -u user1,user2 -p pass1,pass2 --profile all \
              -g 21,25,80,443 -t 1000 -T 3 --graph d3_tree_v2_graph -v --skip-service-discovery

  test-docker-image-build:
    name: Test Docker ${{ matrix.docker-version }} image build
    needs:
      - run-tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        docker-version:
          - '24.0.6-1~ubuntu.22.04~jammy'
          - '23.0.6-1~ubuntu.22.04~jammy'
          - '20.10.13~3-0~ubuntu-jammy'
    steps:
      - name: Uninstall pre-installed Docker
        run: |
          sudo apt-get remove docker-ce docker-ce-cli

        # https://docs.docker.com/engine/install/ubuntu/#install-using-the-repository
      - name: Install Docker ${{ matrix.docker-version }}
        run: |
          sudo apt-get update
          sudo apt-get install ca-certificates curl gnupg
          sudo install -m 0755 -d /etc/apt/keyrings
          curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
          sudo chmod a+r /etc/apt/keyrings/docker.gpg
          echo \
            "deb [arch="$(dpkg --print-architecture)" signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
            "$(. /etc/os-release && echo "$VERSION_CODENAME")" stable" | \
            sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
          sudo apt-get update
          sudo apt-get install docker-ce=5:${{ matrix.docker-version }} docker-ce-cli=5:${{ matrix.docker-version }}

      - name: Check out repository
        uses: actions/checkout@v4

      - name: Print Docker version
        run: docker -v

      - name: Build Nettacker image
        run: docker build . -t nettacker

      - name: Run pip install
        run: docker run nettacker pip install .

  publish-nettacker-dev-to-docker-registry:
    name: Publish nettacker:dev Docker image
    if: |
      github.repository == 'owasp/nettacker' &&
      github.event_name == 'push' &&
      github.ref_name == 'master'
    needs:
      - test-docker-image
      - test-docker-image-build
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile
          push: true
          tags: owasp/nettacker:dev

  publish-nettacker-latest-to-docker-registry:
    name: Publish nettacker:latest Docker image
    if: |
      github.repository == 'owasp/nettacker' &&
      github.event_name == 'push' &&
      startsWith(github.event.ref, 'refs/tags/v')
    needs:
      - test-docker-image
      - test-docker-image-build
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: Dockerfile
          push: true
          tags: owasp/nettacker:latest

  publish-to-test-pypi:
    name: Publish Test PyPI package
    if: |
      github.repository == 'OWASP/Nettacker' &&
      github.event_name == 'push' &&
      github.ref_name == 'master'
    environment: dev
    needs:
      - test-build-package
    permissions:
      contents: read
      id-token: write
    runs-on: ubuntu-latest
    steps:
      - name: Get package artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist

      - name: Publish package distributions to Test PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          repository-url: https://test.pypi.org/legacy/
          skip-existing: true

  publish-to-pypi:
    name: Publish PyPI package
    if: |
      github.repository == 'OWASP/Nettacker' &&
      github.event_name == 'push' &&
      startsWith(github.event.ref, 'refs/tags/')
    environment: release
    needs:
      - test-build-package
    permissions:
      contents: read
      id-token: write
    runs-on: ubuntu-latest
    steps:
      - name: Get package artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist

      - name: Publish package distributions to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
