info:
  name: ivanti_epmm_lastpatcheddate_scan
  author: OWASP Nettacker Team
  severity: 3
  description: <PERSON><PERSON> EPMM Last Patched Date Scan
  reference:
  profiles:
    - scan
    - http
    - ivanti
    - low_severity

payloads:
  - library: http
    steps:
      - method: head
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/mifs/css/pages/userlogin.css"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: and
          log: "response_dependent['headers']['Last-Modified']"
          conditions:
            status_code:
              regex: "200"
              reverse: false
            headers:
              Last-Modified:
                regex: .*  
                reverse: false
              Content-Type:
                regex: "css"
                reverse: false  
