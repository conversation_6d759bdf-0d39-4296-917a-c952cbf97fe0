OWASP Nettacker core functions
==============================

OWASP Nettacker core functions are stored in here.

* `alert.py` user alerts and printing functions
* `args_loader.py` ARGV commands and apply rules
* `color.py` color founds for windows and linux/mac
* `compatible.py` compatibility functions
* `die.py` exit functions
* `graph.py` graph representation
* `ip.py` IPv4 and IPv6 functions
* `load_modules.py` load modules, requirements, paths functions
* `messages.py` class messages
* `parse.py` parse the ARGV and pass it
* `scan_targets.py` start new attacks and multi-processing managements
* `socks_proxy.py` use SOCKS5 proxy 
* `targets.py` process, calculate and count targets
* `time.py` time functions
* `utility.py` support functions