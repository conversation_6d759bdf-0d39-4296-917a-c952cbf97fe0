info:
  name: content_type_options_vuln
  author: OWASP Nettacker Team
  severity: 2
  description:
  reference:
  profiles:
    - vuln
    - vulnerability
    - http
    - low_severity

payloads:
  - library: http
    steps:
      - method: get
        timeout: 3
        headers:
          User-Agent: "{user_agent}"
        allow_redirects: false
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        response:
          condition_type: or
          conditions:
            headers:
              X-Content-Type-Options:
                regex: ^((?!nosniff).)+$
                reverse: false
